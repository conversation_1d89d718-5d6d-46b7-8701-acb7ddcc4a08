import { Injectable, Inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { AuthClient, LoginRequest, UserCreateRequest, UserResponse, TokenResponse } from '../client/auth-client';
import { API_BASE_URL } from '../client/client';

export interface AuthState {
  isAuthenticated: boolean;
  user: UserResponse | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly authStateSubject = new BehaviorSubject<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    loading: false,
    error: null
  });

  public readonly authState$ = this.authStateSubject.asObservable();
  private readonly authClient: AuthClient;

  constructor(
    @Inject(API_BASE_URL) private baseUrl: string,
    private router: Router
  ) {
    this.authClient = new AuthClient(this.baseUrl);
    this.loadStoredAuth();
  }

  get currentState(): AuthState {
    return this.authStateSubject.value;
  }

  get isAuthenticated(): boolean {
    return this.currentState.isAuthenticated;
  }

  get currentUser(): UserResponse | null {
    return this.currentState.user;
  }

  private updateState(updates: Partial<AuthState>): void {
    const currentState = this.authStateSubject.value;
    this.authStateSubject.next({ ...currentState, ...updates });
  }

  private loadStoredAuth(): void {
    try {
      const token = localStorage.getItem('auth_token');
      const userJson = localStorage.getItem('auth_user');
      
      if (token && userJson) {
        const user = JSON.parse(userJson) as UserResponse;
        this.authClient.setToken(token);
        this.updateState({
          isAuthenticated: true,
          user,
          token,
          error: null
        });
      }
    } catch (error) {
      console.error('Error loading stored auth:', error);
      this.clearStoredAuth();
    }
  }

  private storeAuth(token: string, user: UserResponse): void {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('auth_user', JSON.stringify(user));
  }

  private clearStoredAuth(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
  }

  async login(credentials: LoginRequest): Promise<void> {
    this.updateState({ loading: true, error: null });

    try {
      const tokenResponse: TokenResponse = await this.authClient.login(credentials);
      const user: UserResponse = await this.authClient.getCurrentUser();

      this.storeAuth(tokenResponse.access_token, user);
      this.updateState({
        isAuthenticated: true,
        user,
        token: tokenResponse.access_token,
        loading: false,
        error: null
      });

      // Navigate to dashboard after successful login
      this.router.navigate(['/dashboard']);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      this.updateState({
        loading: false,
        error: errorMessage
      });
      throw error;
    }
  }

  async register(userData: UserCreateRequest): Promise<void> {
    this.updateState({ loading: true, error: null });

    try {
      const user: UserResponse = await this.authClient.register(userData);
      
      // After successful registration, automatically log in
      await this.login({
        username: userData.username,
        password: userData.password
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      this.updateState({
        loading: false,
        error: errorMessage
      });
      throw error;
    }
  }

  logout(): void {
    this.authClient.setToken(undefined);
    this.clearStoredAuth();
    this.updateState({
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null
    });

    // Navigate to login page
    this.router.navigate(['/auth/login']);
  }

  async refreshUser(): Promise<void> {
    if (!this.currentState.token) {
      return;
    }

    try {
      const user = await this.authClient.getCurrentUser();
      this.updateState({ user });
      this.storeAuth(this.currentState.token, user);
    } catch (error) {
      console.error('Error refreshing user:', error);
      // If refresh fails, logout the user
      this.logout();
    }
  }

  clearError(): void {
    this.updateState({ error: null });
  }
}

import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface NavItem {
  label: string;
  icon: string;
  route?: string;
  children?: NavItem[];
  expanded?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  private _expanded = new BehaviorSubject<boolean>(false);
  expanded$ = this._expanded.asObservable();

  private _menuItems: NavItem[] = [
    {
      label: 'Dashboard',
      icon: 'pi pi-home',
      route: '/dashboard'
    },
    {
      label: 'Documents',
      icon: 'pi pi-file-pdf',
      route: '/documents'
    },
    {
      label: 'Files',
      icon: 'pi pi-folder',
      route: '/'
    },
    {
      label: 'Analytics',
      icon: 'pi pi-chart-bar',
      route: '/analytics'
    },
    {
      label: 'Settings',
      icon: 'pi pi-cog',
      children: [
        {
          label: 'User Settings',
          icon: 'pi pi-user',
          route: '/settings/user'
        },
        {
          label: 'System Settings',
          icon: 'pi pi-server',
          route: '/settings/system'
        }
      ]
    }
  ];

  constructor() { }

  getMenuItems(): NavItem[] {
    return this._menuItems;
  }

  setExpanded(expanded: boolean): void {
    this._expanded.next(expanded);
  }

  toggleExpanded(): void {
    this._expanded.next(!this._expanded.value);
  }
}

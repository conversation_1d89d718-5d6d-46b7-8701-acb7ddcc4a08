// Lightweight, type‑safe wrapper for the three authentication endpoints
// defined in the FastAPI OpenAPI schema.

import {Injectable} from '@angular/core';

export interface TokenResponse {
  /** JWT access token */
  access_token: string;
  /** Token type (always "bearer" for this API) */
  token_type: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  /** Optional OAuth2 scope string (defaults to empty) */
  scope?: string;
}

export interface UserCreateRequest {
  /** Unique username */
  username: string;
  /** User email address */
  email: string;
  /** Plain‑text password; will be hashed server‑side */
  password: string;
}

export interface UserResponse {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
}

export interface ErrorResponse {
  detail: string;
}

/**
 * AuthClient – minimal, dependency‑free client for the FastAPI auth endpoints.
 *
 * All methods return typed payloads and throw an Error (with the server‑provided
 * `detail` if available) when the HTTP status is not 2xx.
 */
@Injectable({
  providedIn: "root"
})
export class AuthClient {
  private token?: string;

  /**
   * @param baseUrl Base URL of the FastAPI service – e.g. "https://api.example.com" (NO trailing slash)
   * @param fetchFn  Custom fetch implementation (defaults to global `fetch`)
   */
  constructor(
    private readonly baseUrl: string,
    private readonly fetchFn: typeof fetch = fetch,
  ) {}

  /** Replace or clear the bearer token used by `getCurrentUser`. */
  setToken(token?: string) {
    this.token = token;
  }

  /** Helper to build absolute URLs without worrying about slashes. */
  private buildUrl(path: string): string {
    return `${this.baseUrl.replace(/\/+$/, "")}${path}`;
  }

  /** Generic JSON response handler with basic error propagation. */
  private async handleJson<T>(response: Response): Promise<T> {
    const data = (await response.json()) as unknown;
    if (!response.ok) {
      const message = (data as ErrorResponse)?.detail ?? `${response.status} ${response.statusText}`;
      throw new Error(message);
    }
    return data as T;
  }

  /**
   * POST /api/token – Exchange username/password for a JWT access token.
   */
  async login({ username, password, scope = "" }: LoginRequest): Promise<TokenResponse> {
    const body = new URLSearchParams({ grant_type: "password", username, password, scope }).toString();

    const res = await this.fetchFn(this.buildUrl("/api/token"), {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });

    const tokenResponse = await this.handleJson<TokenResponse>(res);
    // Optionally store the token for subsequent requests
    this.token = tokenResponse.access_token;
    return tokenResponse;
  }

  /**
   * POST /api/users – Register a new user account.
   */
  async register(request: UserCreateRequest): Promise<UserResponse> {
    const res = await this.fetchFn(this.buildUrl("/api/users"), {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(request),
    });

    return this.handleJson<UserResponse>(res);
  }

  /**
   * GET /api/users/me – Fetch the profile of the currently authenticated user.
   *
   * If a token is supplied as an argument it overrides the internally‑stored one.
   */
  async getCurrentUser(tokenOverride?: string): Promise<UserResponse> {
    const token = tokenOverride ?? this.token;
    if (!token) {
      throw new Error("getCurrentUser requires a bearer token – call login() first or supply one explicitly");
    }

    const res = await this.fetchFn(this.buildUrl("/api/users/me"), {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return this.handleJson<UserResponse>(res);
  }
}

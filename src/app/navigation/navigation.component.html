<!-- Mobile menu toggle button - only visible on mobile -->
<button *ngIf="isMobile" class="mobile-menu-toggle" (click)="toggleMobileMenu()" [class.active]="isMobileMenuOpen">
  <i class="pi" [ngClass]="isMobileMenuOpen ? 'pi-times' : 'pi-bars'"></i>
</button>

<!-- Mobile menu overlay -->
<div *ngIf="isMobile" class="mobile-menu-overlay" [class.active]="isMobileMenuOpen" (click)="closeMobileMenu()"></div>

<!-- Navigation container with mobile class when on mobile -->
<div class="nav-container"
     [class.expanded]="expanded || hovering"
     [class.mobile]="isMobile"
     [class.mobile-open]="isMobileMenuOpen">
  <div class="nav-header">
    <div class="logo-container">
      <i class="pi pi-file-pdf text-red-600 text-2xl"></i>
      <span class="logo-text">PDF Viewer</span>
    </div>
    <button class="toggle-btn" (click)="toggleExpanded()" *ngIf="!isMobile">
      <i class="pi" [ngClass]="expanded ? 'pi-chevron-left' : 'pi-chevron-right'"></i>
    </button>
    <button class="close-mobile-btn" (click)="closeMobileMenu()" *ngIf="isMobile">
      <i class="pi pi-times"></i>
    </button>
  </div>

  <div class="nav-menu">
    <ul class="menu-list">
      <li *ngFor="let item of menuItems" class="menu-item" [class.has-submenu]="item.children && item.children.length > 0">
        <a
          *ngIf="item.route && !item.children"
          [routerLink]="item.route"
          routerLinkActive="active-link"
          [routerLinkActiveOptions]="{exact: item.route === '/'}"
          class="menu-link"
          pRipple
          (click)="onNavLinkClick()"
        >
          <i class="menu-icon" [ngClass]="item.icon"></i>
          <span class="menu-label">{{ item.label }}</span>
        </a>

        <div
          *ngIf="item.children"
          class="submenu-trigger"
          (click)="toggleSubmenu(item, $event)"
          pRipple
        >
          <div class="menu-link">
            <i class="menu-icon" [ngClass]="item.icon"></i>
            <span class="menu-label">{{ item.label }}</span>
            <i class="submenu-icon pi" [ngClass]="item.expanded ? 'pi-chevron-down' : 'pi-chevron-right'"></i>
          </div>
        </div>

        <ul *ngIf="item.children && item.expanded" class="submenu">
          <li *ngFor="let child of item.children" class="submenu-item">
            <a
              [routerLink]="child.route"
              routerLinkActive="active-link"
              [routerLinkActiveOptions]="{exact: child.route === '/'}"
              class="submenu-link"
              pRipple
              (click)="onNavLinkClick()"
            >
              <i class="submenu-icon" [ngClass]="child.icon"></i>
              <span class="menu-label submenu-label">{{ child.label }}</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
  </div>

  <div class="nav-footer">
    <div class="user-profile" *ngIf="isAuthenticated && currentUser">
      <div class="user-avatar">
        <i class="pi pi-user"></i>
      </div>
      <div class="user-info">
        <span class="user-name">{{ currentUser.username }}</span>
        <span class="user-role">{{ currentUser.email }}</span>
      </div>
      <button
        pButton
        icon="pi pi-sign-out"
        class="logout-btn"
        (click)="logout()"
        [title]="'Logout'"
        severity="secondary"
        size="small">
      </button>
    </div>

    <div class="auth-prompt" *ngIf="!isAuthenticated">
      <a routerLink="/auth/login" class="login-link" (click)="onNavLinkClick()">
        <i class="pi pi-sign-in"></i>
        <span class="login-text">Sign In</span>
      </a>
    </div>
  </div>
</div>

import {ApplicationConfig, importProvidersFrom, provideZoneChangeDetection} from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';

import { routes } from './app.routes';
import {providePrimeNG} from 'primeng/config';
import Aura from '@primeng/themes/aura';
import {provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';
import {LucideAngularModule} from 'lucide-angular';
import {API_BASE_URL} from '../client/client';
import DefaultPreset from './primeng-presets/default.preset';
import { AuthClient } from '../client/auth-client';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(withInterceptorsFromDi()),
    providePrimeNG({
      ripple: true,
      theme: {
        preset: DefaultPreset,
        options: {
          darkModeSelector: false,
        },
      }
    }),
    { provide: API_BASE_URL, useValue: 'http://localhost:8000' },
    {
      provide: AuthClient,
      useFactory: () => new AuthClient('http://localhost:8000'),
      deps: []
    },

    provideAnimations(),
    importProvidersFrom(LucideAngularModule.pick({}))
  ]
};

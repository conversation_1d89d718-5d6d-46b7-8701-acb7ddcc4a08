import { Routes } from '@angular/router';
import { FileSystemComponent } from './file-system/file-system.component';
import { DashboardComponent } from './mock-pages/dashboard/dashboard.component';
import { DocumentsComponent } from './mock-pages/documents/documents.component';
import { AnalyticsComponent } from './mock-pages/analytics/analytics.component';
import { AuthLayoutComponent } from './auth/auth-layout/auth-layout.component';
import { LoginComponent } from './auth/login/login.component';
import { RegisterComponent } from './auth/register/register.component';

export const routes: Routes = [
  {
    path: '',
    component: FileSystemComponent
  },
  {
    path: 'dashboard',
    component: DashboardComponent
  },
  {
    path: 'documents',
    component: DocumentsComponent
  },
  {
    path: 'analytics',
    component: AnalyticsComponent
  },
  {
    path: 'settings/user',
    component: DashboardComponent // Using dashboard as a placeholder for settings pages
  },
  {
    path: 'settings/system',
    component: DashboardComponent // Using dashboard as a placeholder for settings pages
  },
  {
    path: 'auth',
    component: AuthLayoutComponent,
    children: [
      {
        path: 'login',
        component: LoginComponent
      },
      {
        path: 'register',
        component: RegisterComponent
      },
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      }
    ]
  }
];
